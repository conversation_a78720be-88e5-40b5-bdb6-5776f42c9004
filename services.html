<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Services - Essential Property Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.2);
      }
    </style>

  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-6"
    >
      <div class="container mx-auto px-4 md:px-6">
        <!-- Mobile: Small floating menu on right -->
        <div class="lg:hidden flex justify-end">
          <div
            id="navContainer"
            class="backdrop-blur-sm rounded-2xl px-4 py-3 transition-all duration-300 w-auto"
          >
            <!-- Mobile Menu Button -->
            <button
              id="mobileMenuBtn"
              class="text-white"
              onclick="toggleMobileMenu()"
            >
              <svg
                class="w-7 h-7"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- Desktop: Full width navigation -->
        <div
          id="navContainerDesktop"
          class="hidden lg:block backdrop-blur-sm rounded-2xl px-6 py-4 transition-all duration-300"
        >
          <div class="flex justify-center items-center">
            <!-- Left Navigation -->
            <div class="flex items-center space-x-8 mr-auto">
              <nav>
                <ul class="flex space-x-8">
                  <li>
                    <a
                      href="index.html"
                      class="nav-link hover:text-accent transition text-white"
                      title="Home"
                    >
                      <svg
                        class="w-6 h-6"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                        ></path>
                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                      </svg>
                    </a>
                  </li>
                  <li>
                    <a
                      href="services.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Services</a
                    >
                  </li>

                  <li>
                    <a
                      href="team.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Our Team</a
                    >
                  </li>
                </ul>
              </nav>
            </div>

            <!-- Right Navigation -->
            <div class="flex items-center space-x-4 ml-auto">
              <!-- Emergency Call Button -->
              <a
                href="tel:9076009900"
                class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>

              <!-- Maintenance Button -->
              <a
                href="index.html#contact"
                class="nav-link font-bold hover:bg-white/90 transition bg-white text-primary text-sm px-4 py-3 rounded-md"
                >Maintenance</a
              >

              <!-- Estimate Button -->
              <a
                href="#scheduling"
                class="flex items-center bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Estimate
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden lg:hidden bg-white/95 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50 mt-20 max-h-[calc(100vh-5rem)] overflow-y-auto rounded-b-2xl mx-4"
      >
        <nav class="px-6 py-6">
          <ul class="space-y-4">
            <li>
              <a
                href="index.html"
                class="flex items-center py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-5 h-5 mr-3"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                  ></path>
                  <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                Home
              </a>
            </li>
            <li>
              <a
                href="services.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>

            <li>
              <a
                href="team.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="index.html#contact"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Maintenance
              </a>
            </li>
            <!-- Emergency Call Button -->
            <li class="pt-4">
              <a
                href="tel:9076009900"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>
            </li>
            <!-- Estimate Button -->
            <li>
              <a
                href="#scheduling"
                class="flex items-center w-full bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold justify-center transition mt-2"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Get Estimate
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <div class="min-h-screen bg-gray-50">
      <!-- Hero Section -->
      <section class="relative h-[60vh] flex items-center">
        <div class="absolute inset-0">
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d94af1bd95d.webp"
            alt="Services Hero"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div class="container mx-auto px-4 relative z-10">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              Intentionally pursuing our calling through serving you with
              excellence
            </h1>
            <div class="flex flex-wrap gap-4 justify-center">
              <div
                class="flex items-center bg-primary/80 px-6 py-3 rounded-full"
              >
                <svg
                  class="w-5 h-5 text-accent mr-2"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <polygon
                    points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                  />
                </svg>
                <span class="font-semibold">Licensed & Insured</span>
              </div>
              <div
                class="flex items-center bg-primary/80 px-6 py-3 rounded-full"
              >
                <span class="font-semibold">24/7 Emergency Service</span>
              </div>
              <div
                class="flex items-center bg-primary/80 px-6 py-3 rounded-full"
              >
                <span class="font-semibold">Free Estimates</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- User Types Section -->
      <section id="user-types" class="py-20 bg-white">
        <div class="container mx-auto px-4 md:px-6">
          <h2
            class="text-3xl md:text-4xl font-bold text-center mb-8 text-primary"
          >
            Specialized Services for Each Client Type
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto text-center mb-12">
            We understand that different clients have different requirements.
            That's why we offer tailored services for each of our client types.
          </p>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Property Owners -->
            <div
              class="bg-gray-50 rounded-lg shadow-lg overflow-hidden hover-grow"
            >
              <div
                class="h-48 bg-cover bg-center"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d584d124f429.webp');
                "
              >
                <div
                  class="h-full w-full bg-primary/50 flex items-center justify-center"
                >
                  <h3 class="text-2xl font-bold text-white">Property Owners</h3>
                </div>
              </div>
              <div class="p-6">
                <ul class="space-y-3 mb-6">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Expert diagnosis of home issues</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Comprehensive home maintenance</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Quality remodeling and repairs</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Skilled craftsmanship you can trust</span>
                  </li>
                </ul>
                <p class="text-gray-600 mb-6">
                  We're passionate about diagnosing issues, maintaining homes,
                  and delivering quality remodeling and repairs. Our skilled
                  team enjoys the work and takes pride in every project.
                </p>
              </div>
            </div>

            <!-- Real Estate Agents -->
            <div
              class="bg-gray-50 rounded-lg shadow-lg overflow-hidden hover-grow"
            >
              <div
                class="h-48 bg-cover bg-center"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a253c12ce1cdb6.webp');
                "
              >
                <div
                  class="h-full w-full bg-primary/50 flex items-center justify-center"
                >
                  <h3 class="text-2xl font-bold text-white">
                    Real Estate Agents
                  </h3>
                </div>
              </div>
              <div class="p-6">
                <ul class="space-y-3 mb-6">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Quick handling of inspection items</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Prompt response to addenda requests</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Licensed, bonded & insured work</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Guaranteed tight deadlines</span>
                  </li>
                </ul>
                <p class="text-gray-600 mb-6">
                  We handle inspection items and addenda quickly with licensed,
                  bonded, and insured structural work.
                  No subcontractors means we guarantee tight deadlines.
                </p>
              </div>
            </div>

            <!-- Property Managers -->
            <div
              class="bg-gray-50 rounded-lg shadow-lg overflow-hidden hover-grow"
            >
              <div
                class="h-48 bg-cover bg-center"
                style="
                  background-image: url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc43828ab829c6.webp');
                "
              >
                <div
                  class="h-full w-full bg-primary/50 flex items-center justify-center"
                >
                  <h3 class="text-2xl font-bold text-white">
                    Property Managers
                  </h3>
                </div>
              </div>
              <div class="p-6">
                <ul class="space-y-3 mb-6">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Efficient make-ready services</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Responsive tenant repair services</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Expert coordination of services</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>Complete repairs from foundation to attic</span>
                  </li>
                </ul>
                <p class="text-gray-600 mb-6">
                  We provide make-readies, tenant repairs, and expert
                  coordination for all your rental property needs. Our
                  comprehensive services cover everything from foundation to
                  attic.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Services Grid -->
      <section id="services" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
          <h2
            class="text-3xl md:text-4xl font-bold text-center mb-12 text-primary"
          >
            Created to Serve:
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto text-center mb-12">
            Intentionally pursuing our calling through serving your property
            needs with excellence.
          </p>
          <div class="grid md:grid-cols-3 gap-8">


            <!-- Kitchen & Bath -->
            <div class="bg-gray-50 rounded-lg p-6 shadow-lg">
              <h3 class="text-xl font-bold text-primary mb-4">
                Kitchen & Bath
              </h3>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Full Kitchen Remodels</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Bathroom Renovations</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Cabinet Refinishing</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Countertop Installation</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Tile Work</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Plumbing Fixtures</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Custom Storage Solutions</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Lighting Installation</span>
                </li>
              </ul>
            </div>

            <!-- Interior Improvements -->
            <div class="bg-gray-50 rounded-lg p-6 shadow-lg">
              <h3 class="text-xl font-bold text-primary mb-4">
                Interior Improvements
              </h3>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Drywall Installation & Repair</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Interior Painting</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Flooring Installation</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Crown Molding</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Custom Built-ins</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Room Additions</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Basement Finishing</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Door Installation</span>
                </li>
              </ul>
            </div>

            <!-- Exterior Services -->
            <div class="bg-gray-50 rounded-lg p-6 shadow-lg">
              <h3 class="text-xl font-bold text-primary mb-4">
                Exterior Services
              </h3>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Deck</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Siding Repair and Installation</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Window Repair and Replacement</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Crawlspace Repairs</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Attic Repairs</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Weatherproofing</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Diagnosing Water Related Issues</span>
                </li>
                <li class="flex items-start">
                  <svg
                    class="w-5 h-5 text-accent mr-2 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span>Rot Repair</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- Benefits Section -->
      <section class="py-20 bg-primary">
        <div class="container mx-auto px-4">
          <h2
            class="text-3xl md:text-4xl font-bold text-center mb-12 text-white"
          >
            Why Regular Maintenance Matters
          </h2>
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Protect Your Investment -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <div class="flex items-center mb-4">
                <svg
                  class="w-8 h-8 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
                </svg>
                <h3 class="text-xl font-bold text-primary ml-3">
                  Protect Your Investment
                </h3>
              </div>
              <p class="text-gray-600">
                Regular maintenance helps preserve and increase your home's
                value over time, preventing costly repairs.
              </p>
            </div>

            <!-- Save Money Long-Term -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <div class="flex items-center mb-4">
                <svg
                  class="w-8 h-8 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <line x1="12" y1="1" x2="12" y2="23"></line>
                  <path
                    d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"
                  ></path>
                </svg>
                <h3 class="text-xl font-bold text-primary ml-3">
                  Save Money Long-Term
                </h3>
              </div>
              <p class="text-gray-600">
                Preventive maintenance costs far less than emergency repairs and
                system replacements.
              </p>
            </div>

            <!-- Extend Equipment Life -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <div class="flex items-center mb-4">
                <svg
                  class="w-8 h-8 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"
                  />
                </svg>
                <h3 class="text-xl font-bold text-primary ml-3">
                  Extend Equipment Life
                </h3>
              </div>
              <p class="text-gray-600">
                Well-maintained systems and appliances last longer, giving you
                better return on investment.
              </p>
            </div>

            <!-- Peace of Mind -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <div class="flex items-center mb-4">
                <svg
                  class="w-8 h-8 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"
                  ></path>
                </svg>
                <h3 class="text-xl font-bold text-primary ml-3">
                  Peace of Mind
                </h3>
              </div>
              <p class="text-gray-600">
                Regular inspections catch potential issues before they become
                major problems.
              </p>
            </div>

            <!-- Energy Efficiency -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <div class="flex items-center mb-4">
                <svg
                  class="w-8 h-8 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M3 9l9-7 9 7v11a2 2  0 0 1-2 2H5a2 2 0 0 1-2-2z"
                  ></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                <h3 class="text-xl font-bold text-primary ml-3">
                  Energy Efficiency
                </h3>
              </div>
              <p class="text-gray-600">
                Properly maintained systems run more efficiently, reducing your
                energy bills.
              </p>
            </div>

            <!-- Convenience -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
              <div class="flex items-center mb-4">
                <svg
                  class="w-8 h-8 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <h3 class="text-xl font-bold text-primary ml-3">Convenience</h3>
              </div>
              <p class="text-gray-600">
                Scheduled maintenance prevents unexpected breakdowns and
                emergency repairs.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Project Images Grid -->
      <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
          <h2
            class="text-3xl md:text-4xl font-bold text-center mb-12 text-primary"
          >
            Our Work in Action
          </h2>

          <!-- Service Image Galleries -->
          <div class="space-y-16">
            <!-- Emergency Services Gallery -->
            <div>
              <h3 class="text-2xl font-bold text-primary mb-6 text-center">
                Emergency Services
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7d97df52dbc67c83e.webp"
                    alt="Flooded Floor - Before Emergency Response"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Flood Damage - Before</h4>
                      <p class="text-sm opacity-90">Emergency Response</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc435f1db829c5.webp"
                    alt="Flooded Floor - After Restoration"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Flood Damage - After</h4>
                      <p class="text-sm opacity-90">Complete Restoration</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/6850636842a25390f2e17a32.webp"
                    alt="Emergency Response Team"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Emergency Repairs</h4>
                      <p class="text-sm opacity-90">Immediate Response</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Remodeling Gallery -->
            <div>
              <h3 class="text-2xl font-bold text-primary mb-6 text-center">
                Kitchen & Bath Remodeling
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d584d124f429.webp"
                    alt="Kitchen After Remodel"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Kitchen - After</h4>
                      <p class="text-sm opacity-90">
                        Complete Kitchen Transformation
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7bd2be46566af3745.webp"
                    alt="Shower Completed"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Shower - Completed</h4>
                      <p class="text-sm opacity-90">
                        Luxury Bathroom Renovation
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d4b191bd959.webp"
                    alt="Kitchen After - Landscape View"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Custom Cabinetry</h4>
                      <p class="text-sm opacity-90">Built-in Solutions</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>


            <!-- Maintenance Services Gallery -->
            <div>
              <h3 class="text-2xl font-bold text-primary mb-6 text-center">
                Maintenance Services
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc434dcdb829c8.webp"
                    alt="EPS Maintenance Services"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Professional Maintenance</h4>
                      <p class="text-sm opacity-90">Expert Care</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d6cfc1bd95b.webp"
                    alt="EPS Painting Services"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Professional Painting</h4>
                      <p class="text-sm opacity-90">Interior & Exterior</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af8681f2d348c1bd95e.webp"
                    alt="Window Sill - Before Repair"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Window Sill - Before</h4>
                      <p class="text-sm opacity-90">Repair Needed</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af76b97b26856fdc209.webp"
                    alt="Window Sill - After Repair"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Seasonal Maintenance</h4>
                      <p class="text-sm opacity-90">Year-Round Care</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Cleaning Services Gallery -->
            <div>
              <h3 class="text-2xl font-bold text-primary mb-6 text-center">
                Professional Cleaning Services
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d5b3d1bd95a.webp"
                    alt="Shower Demo - Cleaning Phase"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Post-Demo Cleanup</h4>
                      <p class="text-sm opacity-90">Thorough & Complete</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc435f1db829c5.webp"
                    alt="Flooded Floor - After Cleaning"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Flood Cleanup - After</h4>
                      <p class="text-sm opacity-90">Complete Restoration</p>
                    </div>
                  </div>
                </div>
                <div
                  class="relative aspect-video rounded-lg overflow-hidden shadow-lg hover-grow"
                >
                  <img
                    src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a25329ede1cdb5.webp"
                    alt="Waiting for Doors - Clean Workspace"
                    class="w-full h-full object-cover"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end"
                  >
                    <div class="p-4 text-white">
                      <h4 class="font-bold">Post-Construction</h4>
                      <p class="text-sm opacity-90">Final Cleanup</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      <!-- Service Options Section -->
      <section class="py-20 bg-primary text-white">
        <div class="container mx-auto px-4 md:px-6">
          <h2
            class="text-3xl md:text-5xl font-bold text-center mb-12 text-white"
          >
            Choose Your Service Option
          </h2>

          <!-- Emergency Services -->
          <div class="mb-20">
            <div class="flex flex-col gap-12 items-center">
              <!-- Emergency Services Section -->
              <div
                class="bg-primary text-white rounded-xl p-8 shadow-lg w-full border border-white/20"
              >
                <div class="flex items-center mb-6">
                  <div class="bg-accent rounded-full p-3 mr-4">
                    <svg
                      class="w-8 h-8 text-primary"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </div>
                  <h3 class="text-2xl font-bold">Have an Emergency?</h3>
                </div>

                <ul class="space-y-4 mb-8">
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <span
                      ><strong>Licensed Plumber</strong> for emergencies (leaks,
                      floods, no water)</span
                    >
                  </li>

                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <span
                      >Structural damage (storm, accident, fence repair)</span
                    >
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <span>Heating/cooling failures in extreme weather</span>
                  </li>
                </ul>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="tel:9076009900"
                    class="flex items-center justify-center bg-accent text-primary hover:bg-accent/90 px-6 py-4 rounded-lg text-xl font-semibold transition transform hover:scale-105 whitespace-nowrap"
                  >
                    <svg
                      class="w-6 h-6 mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                      ></path>
                    </svg>
                    Call: (*************
                  </a>
                  <a
                    href="#scheduling"
                    class="flex items-center justify-center bg-white/20 hover:bg-white/30 px-6 py-4 rounded-lg text-xl font-semibold transition"
                  >
                    Request Emergency Service
                  </a>
                </div>
              </div>

              <!-- Scheduling Section -->
              <div
                id="scheduling"
                class="bg-white rounded-xl shadow-xl overflow-hidden w-full"
              >
                <div
                  class="p-6 bg-accent text-primary font-bold text-xl text-center"
                >
                  Schedule Your Service
                </div>
                <iframe
                  src="https://api.leadconnectorhq.com/widget/booking/NuS5BqNCvLPpRUBBllGn"
                  style="
                    width: 100%;
                    border: none;
                    overflow: hidden;
                    height: 600px;
                  "
                  scrolling="no"
                  id="NuS5BqNCvLPpRUBBllGn_1747152639241"
                ></iframe>
                <script
                  src="https://link.msgsndr.com/js/form_embed.js"
                  type="text/javascript"
                ></script>
              </div>
            </div>
          </div>

          <!-- Scheduled Maintenance/Repairs -->
          <div class="mb-20">
            <h3 class="text-2xl font-bold text-white mb-6 text-center">
              Schedule Maintenance & Repairs
            </h3>
            <p class="text-lg text-white/80 max-w-3xl mx-auto text-center mb-8">
              All the services above with the ability to be scheduled as they
              are not emergencies. Use our convenient booking system to schedule
              your service.
            </p>
            <div class="flex justify-center">
              <a
                href="#scheduling"
                class="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
              >
                Schedule Maintenance
              </a>
            </div>
          </div>

          <!-- Complex Repairs/Remodels -->
          <div>
            <h3 class="text-2xl font-bold text-white mb-6 text-center">
              Schedule Estimate for Complex Repairs & Remodels
            </h3>
            <p class="text-lg text-white/80 max-w-3xl mx-auto text-center mb-8">
              For more complex projects including kitchen and bathroom remodels,
              foundation/attic/structural repairs, flooring, doors, and windows.
              We provide comprehensive solutions for your property needs.
            </p>
            <div class="flex justify-center">
              <a
                href="#scheduling"
                class="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
              >
                Request an Estimate
              </a>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="py-16 bg-primary">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              Ready to Start Your Project?
            </h2>
            <p class="text-xl mb-8 opacity-90">
              Get in touch today for a free consultation and estimate.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="index.html#contact"
                class="px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition"
              >
                Get Your Free Estimate
              </a>
              <a
                href="tel:9076009900"
                class="px-8 py-4 bg-white hover:bg-gray-100 text-primary font-semibold rounded-md transition flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Call Now: (*************
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer class="bg-primary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
              alt="Essential Property Services Logo"
              class="h-16 mb-4"
            />
            <p class="text-gray-300 mb-4">
              Creating to serve with excellence, intentionally pursuing our
              calling through serving you with humility and professionalism.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-accent hover:text-accent/80 transition">
                <span class="sr-only">Facebook</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
              </a>
              <a href="#" class="text-accent hover:text-accent/80 transition">
                <span class="sr-only">Instagram</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:9076009900" class="hover:text-accent transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-accent transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                  ></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>Serving Anchorage, AK & Surrounding Areas</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Alaska General Contractor License 217482</li>
              <li>MOA License CON14200</li>
              <li>Fully Insured and Bonded</li>
              <li>Available 24/7/364</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Essential Property Services LLC. All rights reserved.
          </p>
          <p class="mt-2">
            <a href="privacy.html" class="hover:text-accent transition"
              >Privacy Policy</a
            >
            |
            <a href="terms.html" class="hover:text-accent transition"
              >Terms of Service</a
            >
          </p>
        </div>
      </div>
    </footer>

    <script>
      // Header scroll effect with transparent/white background transition
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navContainer = document.getElementById("navContainer");
        const navContainerDesktop = document.getElementById(
          "navContainerDesktop"
        );
        const navLinks = document.querySelectorAll(".nav-link");
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");

        if (window.scrollY > 20) {
          // Scrolled state - white background
          header.classList.remove("py-6");
          header.classList.add("py-4");

          // Apply to both mobile and desktop containers
          if (navContainer) {
            navContainer.classList.add("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.add("bg-white/95", "shadow-lg");
          }

          // Change text colors to dark (except maintenance button which stays white bg)
          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              // Maintenance button stays white with purple text
              link.classList.remove(
                "text-white",
                "hover:text-accent",
                "bg-white",
                "hover:bg-white/90"
              );
              link.classList.add(
                "text-primary",
                "bg-gray-100",
                "hover:bg-gray-200"
              );
            } else {
              link.classList.remove("text-white", "hover:text-accent");
              link.classList.add("text-gray-800", "hover:text-primary");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-white");
            mobileMenuBtn.classList.add("text-primary");
          }
        } else {
          // Top of page - transparent background
          header.classList.add("py-6");
          header.classList.remove("py-4");

          // Remove from both mobile and desktop containers
          if (navContainer) {
            navContainer.classList.remove("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.remove("bg-white/95", "shadow-lg");
          }

          // Change text colors to white
          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              // Maintenance button is white with purple text
              link.classList.remove(
                "text-primary",
                "bg-gray-100",
                "hover:bg-gray-200"
              );
              link.classList.add(
                "text-primary",
                "bg-white",
                "hover:bg-white/90"
              );
            } else {
              link.classList.remove("text-gray-800", "hover:text-primary");
              link.classList.add("text-white", "hover:text-accent");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-primary");
            mobileMenuBtn.classList.add("text-white");
          }
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }
    </script>
  </body>
</html>
